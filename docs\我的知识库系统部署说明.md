# 我的知识库系统部署说明

## 📋 系统概述

本系统基于若依框架开发，实现了完整的知识库权限管理功能，包括：
- 知识库权限管理（三级权限：read/write/admin）
- 我的知识库管理（支持创建、编辑、删除）
- 批量授权功能
- 权限可视化展示

## 🗄️ 数据库部署

### 1. 执行SQL脚本
```sql
-- 执行以下脚本文件
source sql/create_simple_knowledge_system.sql;
```

### 2. 验证创建结果
```sql
-- 查看权限表
DESCRIBE knowledge_base_permission;

-- 查看菜单结构
SELECT menu_name, perms, parent_id FROM sys_menu 
WHERE menu_name LIKE '%我的知识库%' OR perms LIKE 'knowledge:%';

-- 查看角色
SELECT role_name, role_key FROM sys_role WHERE role_key = 'knowledge_admin';
```

## 🔧 后端部署

### 1. 新增的文件列表
```
ruoyi-system/src/main/java/com/ruoyi/knowledge/domain/KnowledgeBasePermission.java
ruoyi-system/src/main/java/com/ruoyi/knowledge/mapper/KnowledgeBasePermissionMapper.java
ruoyi-system/src/main/java/com/ruoyi/knowledge/service/IKnowledgeBasePermissionService.java
ruoyi-system/src/main/java/com/ruoyi/knowledge/service/impl/KnowledgeBasePermissionServiceImpl.java
ruoyi-system/src/main/resources/mapper/knowledge/KnowledgeBasePermissionMapper.xml
ruoyi-admin/src/main/java/com/ruoyi/web/controller/knowledge/KnowledgeBasePermissionController.java
ruoyi-admin/src/main/java/com/ruoyi/web/controller/knowledge/MyKnowledgeBaseController.java
```

### 2. 修改的文件列表
```
ruoyi-system/src/main/java/com/ruoyi/knowledge/domain/KnowledgeBase.java (添加辅助字段)
ruoyi-system/src/main/resources/mapper/knowledge/KnowledgeBaseMapper.xml (添加查询条件)
```

### 3. 重启服务
```bash
# 重新编译并启动后端服务
mvn clean compile
java -jar ruoyi-admin.jar
```

## 🎨 前端部署

### 1. 新增的文件列表
```
ruoyi-ui/RuoYi-Vue3/src/api/knowledge/permission.js
ruoyi-ui/RuoYi-Vue3/src/api/knowledge/my.js
ruoyi-ui/RuoYi-Vue3/src/views/knowledge/my/index.vue
```

### 2. 修改的文件列表
```
ruoyi-ui/RuoYi-Vue3/src/views/knowledge/permission/index.vue (简化版适配)
```

### 3. 重启前端服务
```bash
# 重新启动前端服务
npm run dev
```

## 👥 用户权限配置

### 1. 分配知识库管理员角色
```sql
-- 为用户分配知识库管理员角色（示例：用户ID为2）
INSERT INTO sys_user_role (user_id, role_id) 
SELECT 2, role_id FROM sys_role WHERE role_key = 'knowledge_admin';
```

### 2. 权限说明
- **超级管理员**：拥有所有权限
- **知识库管理员**：可以管理知识库权限，授予其他用户访问权限
- **普通用户**：只能访问有权限的知识库

## 🧪 功能测试

### 1. 访问页面
- 我的知识库：`http://localhost/my-knowledge/list`
- 权限管理：`http://localhost/my-knowledge/permission`

### 2. 测试流程
1. **创建知识库**：在"我的知识库"页面创建新知识库
2. **自动授权**：验证创建者自动获得管理员权限
3. **权限管理**：在权限管理页面为其他用户授权
4. **权限验证**：切换用户验证权限是否生效
5. **批量授权**：测试批量授权功能

### 3. 权限层级测试
- **只读权限**：只能查看知识库内容
- **读写权限**：可以编辑知识库内容
- **管理员权限**：可以管理知识库权限

## 🔍 故障排查

### 1. 菜单不显示
```sql
-- 检查菜单是否创建
SELECT * FROM sys_menu WHERE menu_name = '我的知识库';

-- 检查角色菜单关联
SELECT * FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE perms LIKE 'knowledge:%'
);
```

### 2. 权限检查失败
```sql
-- 检查权限表数据
SELECT * FROM knowledge_base_permission;

-- 检查用户角色
SELECT u.user_name, r.role_name FROM sys_user u
JOIN sys_user_role ur ON u.user_id = ur.user_id
JOIN sys_role r ON ur.role_id = r.role_id
WHERE r.role_key = 'knowledge_admin';
```

### 3. API接口报错
- 检查Controller类是否正确扫描
- 检查Service依赖注入是否正常
- 检查Mapper XML文件路径是否正确

## 📊 系统特性

### 1. 设计优势
- **表结构精简**：从15个字段减少到8个字段
- **复用原生表**：利用sys_user、sys_role、sys_menu
- **数据一致性**：避免冗余字段同步问题
- **维护成本低**：关联查询获取用户信息

### 2. 功能特性
- **三级权限体系**：read < write < admin
- **智能权限检查**：支持权限层级判断
- **批量授权**：支持一次性给多个用户授权
- **自动授权**：知识库创建者自动获得管理员权限
- **标签页切换**：全部/我创建的/共享给我的
- **权限可视化**：详情页显示当前用户权限

## 🎯 后续扩展

### 1. 可扩展功能
- 知识库分组管理
- 权限有效期设置
- 权限申请审批流程
- 知识库访问日志
- 权限变更通知

### 2. 性能优化
- 权限缓存机制
- 批量权限检查
- 分页查询优化
- 索引优化建议

---

## 📞 技术支持

如有问题，请检查：
1. 数据库连接是否正常
2. SQL脚本是否执行成功
3. 后端服务是否正常启动
4. 前端路由是否配置正确
5. 用户权限是否正确分配

系统部署完成后，即可正常使用知识库权限管理功能！
