package com.ruoyi.web.controller.knowledge;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.knowledge.domain.KnowledgeBase;
import com.ruoyi.knowledge.domain.KnowledgeBasePermission;
import com.ruoyi.knowledge.service.IKnowledgeBaseService;
import com.ruoyi.knowledge.service.IKnowledgeBasePermissionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 我的知识库Controller
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
@RestController
@RequestMapping("/knowledge/my")
public class MyKnowledgeBaseController extends BaseController
{
    @Autowired
    private IKnowledgeBaseService knowledgeBaseService;
    
    @Autowired
    private IKnowledgeBasePermissionService knowledgeBasePermissionService;

    /**
     * 查询我的知识库列表（我创建的 + 我有权限的）
     */
    @PreAuthorize("@ss.hasPermi('knowledge:my:list')")
    @GetMapping("/list")
    public TableDataInfo list(KnowledgeBase knowledgeBase)
    {
        startPage();
        Long currentUserId = SecurityUtils.getUserId();
        
        // 查询我有权限的知识库
        List<KnowledgeBasePermission> permissions = knowledgeBasePermissionService.selectKnowledgeBasePermissionByUserId(currentUserId);
        
        // 如果没有任何权限，返回空列表
        if (permissions.isEmpty()) {
            return getDataTable(List.of());
        }
        
        // 提取知识库ID列表
        Long[] knowledgeBaseIds = permissions.stream()
            .map(KnowledgeBasePermission::getKnowledgeBaseId)
            .toArray(Long[]::new);
        
        // 查询知识库详情
        knowledgeBase.setIds(knowledgeBaseIds);
        List<KnowledgeBase> list = knowledgeBaseService.selectKnowledgeBaseList(knowledgeBase);
        
        // 为每个知识库添加权限信息
        for (KnowledgeBase kb : list) {
            permissions.stream()
                .filter(p -> p.getKnowledgeBaseId().equals(kb.getId()))
                .findFirst()
                .ifPresent(p -> {
                    // 可以在这里添加权限信息到知识库对象中
                    kb.setRemark(kb.getRemark() + " [权限:" + getPermissionText(p.getPermissionType()) + "]");
                });
        }
        
        return getDataTable(list);
    }

    /**
     * 查询我创建的知识库列表
     */
    @PreAuthorize("@ss.hasPermi('knowledge:my:list')")
    @GetMapping("/created")
    public TableDataInfo listCreated(KnowledgeBase knowledgeBase)
    {
        startPage();
        Long currentUserId = SecurityUtils.getUserId();
        knowledgeBase.setCreatorId(currentUserId);
        List<KnowledgeBase> list = knowledgeBaseService.selectKnowledgeBaseList(knowledgeBase);
        return getDataTable(list);
    }

    /**
     * 查询我有权限的知识库列表（不包括我创建的）
     */
    @PreAuthorize("@ss.hasPermi('knowledge:my:list')")
    @GetMapping("/shared")
    public TableDataInfo listShared(KnowledgeBase knowledgeBase)
    {
        startPage();
        Long currentUserId = SecurityUtils.getUserId();
        
        // 查询我有权限但不是我创建的知识库
        List<KnowledgeBasePermission> permissions = knowledgeBasePermissionService.selectKnowledgeBasePermissionByUserId(currentUserId);
        
        if (permissions.isEmpty()) {
            return getDataTable(List.of());
        }
        
        Long[] knowledgeBaseIds = permissions.stream()
            .map(KnowledgeBasePermission::getKnowledgeBaseId)
            .toArray(Long[]::new);
        
        knowledgeBase.setIds(knowledgeBaseIds);
        knowledgeBase.setExcludeCreatorId(currentUserId); // 排除我创建的
        List<KnowledgeBase> list = knowledgeBaseService.selectKnowledgeBaseList(knowledgeBase);
        
        return getDataTable(list);
    }

    /**
     * 获取知识库详细信息
     */
    @PreAuthorize("@ss.hasPermi('knowledge:my:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        Long currentUserId = SecurityUtils.getUserId();
        
        // 检查权限
        if (!hasKnowledgeBaseAccess(id, currentUserId, "read")) {
            return error("您没有访问此知识库的权限");
        }
        
        KnowledgeBase knowledgeBase = knowledgeBaseService.selectKnowledgeBaseById(id);
        
        // 获取我的权限信息
        KnowledgeBasePermission permission = knowledgeBasePermissionService.selectUserKnowledgeBasePermission(id, currentUserId);
        
        return success().put("knowledgeBase", knowledgeBase).put("permission", permission);
    }

    /**
     * 新增知识库
     */
    @PreAuthorize("@ss.hasPermi('knowledge:my:add')")
    @Log(title = "我的知识库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KnowledgeBase knowledgeBase)
    {
        Long currentUserId = SecurityUtils.getUserId();
        String currentUserName = SecurityUtils.getUsername();
        
        knowledgeBase.setCreatorId(currentUserId);
        knowledgeBase.setCreatorName(currentUserName);
        
        int result = knowledgeBaseService.insertKnowledgeBase(knowledgeBase);
        
        if (result > 0) {
            // 为创建者自动授权管理员权限
            knowledgeBasePermissionService.grantCreatorAdminPermission(knowledgeBase.getId(), currentUserId, currentUserName);
        }
        
        return toAjax(result);
    }

    /**
     * 修改知识库
     */
    @PreAuthorize("@ss.hasPermi('knowledge:my:edit')")
    @Log(title = "我的知识库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KnowledgeBase knowledgeBase)
    {
        Long currentUserId = SecurityUtils.getUserId();
        
        // 检查权限（需要写权限）
        if (!hasKnowledgeBaseAccess(knowledgeBase.getId(), currentUserId, "write")) {
            return error("您没有编辑此知识库的权限");
        }
        
        return toAjax(knowledgeBaseService.updateKnowledgeBase(knowledgeBase));
    }

    /**
     * 删除知识库
     */
    @PreAuthorize("@ss.hasPermi('knowledge:my:remove')")
    @Log(title = "我的知识库", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        Long currentUserId = SecurityUtils.getUserId();
        
        // 检查每个知识库的权限（需要管理员权限）
        for (Long id : ids) {
            if (!hasKnowledgeBaseAccess(id, currentUserId, "admin")) {
                return error("您没有删除知识库 ID:" + id + " 的权限");
            }
        }
        
        // 删除知识库及相关权限
        for (Long id : ids) {
            knowledgeBasePermissionService.deleteKnowledgeBasePermissionByKnowledgeBaseId(id);
        }
        
        return toAjax(knowledgeBaseService.deleteKnowledgeBaseByIds(ids));
    }

    /**
     * 导出我的知识库列表
     */
    @PreAuthorize("@ss.hasPermi('knowledge:my:export')")
    @Log(title = "我的知识库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KnowledgeBase knowledgeBase)
    {
        Long currentUserId = SecurityUtils.getUserId();
        
        // 查询我有权限的知识库
        List<KnowledgeBasePermission> permissions = knowledgeBasePermissionService.selectKnowledgeBasePermissionByUserId(currentUserId);
        
        if (!permissions.isEmpty()) {
            Long[] knowledgeBaseIds = permissions.stream()
                .map(KnowledgeBasePermission::getKnowledgeBaseId)
                .toArray(Long[]::new);
            
            knowledgeBase.setIds(knowledgeBaseIds);
            List<KnowledgeBase> list = knowledgeBaseService.selectKnowledgeBaseList(knowledgeBase);
            
            ExcelUtil<KnowledgeBase> util = new ExcelUtil<KnowledgeBase>(KnowledgeBase.class);
            util.exportExcel(response, list, "我的知识库数据");
        }
    }

    /**
     * 检查用户是否有知识库访问权限
     */
    private boolean hasKnowledgeBaseAccess(Long knowledgeBaseId, Long userId, String requiredPermission) {
        return knowledgeBasePermissionService.hasKnowledgeBasePermission(knowledgeBaseId, userId, requiredPermission);
    }

    /**
     * 获取权限类型的中文描述
     */
    private String getPermissionText(String permissionType) {
        switch (permissionType) {
            case "read": return "只读";
            case "write": return "读写";
            case "admin": return "管理员";
            default: return "未知";
        }
    }
}
