package com.ruoyi.knowledge.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 知识库权限对象 knowledge_base_permission（简化版）
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
public class KnowledgeBasePermission extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 权限ID */
    private Long id;

    /** 知识库ID */
    @Excel(name = "知识库ID")
    private Long knowledgeBaseId;

    /** 知识库名称（关联查询字段，不存储在表中） */
    @Excel(name = "知识库名称")
    private String knowledgeBaseName;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户名称（关联查询字段，不存储在表中） */
    @Excel(name = "用户名称")
    private String userName;

    /** 用户昵称（关联查询字段，不存储在表中） */
    @Excel(name = "用户昵称")
    private String nickName;

    /** 权限类型（read只读，write读写，admin管理员） */
    @Excel(name = "权限类型", readConverterExp = "read=只读,write=读写,admin=管理员")
    private String permissionType;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setKnowledgeBaseId(Long knowledgeBaseId)
    {
        this.knowledgeBaseId = knowledgeBaseId;
    }

    public Long getKnowledgeBaseId()
    {
        return knowledgeBaseId;
    }

    public void setKnowledgeBaseName(String knowledgeBaseName)
    {
        this.knowledgeBaseName = knowledgeBaseName;
    }

    public String getKnowledgeBaseName()
    {
        return knowledgeBaseName;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setUserName(String userName)
    {
        this.userName = userName;
    }

    public String getUserName()
    {
        return userName;
    }

    public void setNickName(String nickName)
    {
        this.nickName = nickName;
    }

    public String getNickName()
    {
        return nickName;
    }

    public void setPermissionType(String permissionType)
    {
        this.permissionType = permissionType;
    }

    public String getPermissionType()
    {
        return permissionType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("knowledgeBaseId", getKnowledgeBaseId())
            .append("knowledgeBaseName", getKnowledgeBaseName())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("nickName", getNickName())
            .append("permissionType", getPermissionType())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
