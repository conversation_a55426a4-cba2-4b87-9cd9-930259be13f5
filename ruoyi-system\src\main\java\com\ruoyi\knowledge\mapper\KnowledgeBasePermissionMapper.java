package com.ruoyi.knowledge.mapper;

import java.util.List;
import com.ruoyi.knowledge.domain.KnowledgeBasePermission;

/**
 * 知识库权限Mapper接口（简化版）
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
public interface KnowledgeBasePermissionMapper 
{
    /**
     * 查询知识库权限
     * 
     * @param id 知识库权限主键
     * @return 知识库权限
     */
    public KnowledgeBasePermission selectKnowledgeBasePermissionById(Long id);

    /**
     * 查询知识库权限列表
     * 
     * @param knowledgeBasePermission 知识库权限
     * @return 知识库权限集合
     */
    public List<KnowledgeBasePermission> selectKnowledgeBasePermissionList(KnowledgeBasePermission knowledgeBasePermission);

    /**
     * 根据用户ID查询知识库权限列表
     * 
     * @param userId 用户ID
     * @return 知识库权限集合
     */
    public List<KnowledgeBasePermission> selectKnowledgeBasePermissionByUserId(Long userId);

    /**
     * 根据知识库ID查询权限列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 知识库权限集合
     */
    public List<KnowledgeBasePermission> selectKnowledgeBasePermissionByKnowledgeBaseId(Long knowledgeBaseId);

    /**
     * 检查用户对知识库的权限
     * 
     * @param knowledgeBaseId 知识库ID
     * @param userId 用户ID
     * @return 知识库权限
     */
    public KnowledgeBasePermission selectUserKnowledgeBasePermission(Long knowledgeBaseId, Long userId);

    /**
     * 新增知识库权限
     * 
     * @param knowledgeBasePermission 知识库权限
     * @return 结果
     */
    public int insertKnowledgeBasePermission(KnowledgeBasePermission knowledgeBasePermission);

    /**
     * 修改知识库权限
     * 
     * @param knowledgeBasePermission 知识库权限
     * @return 结果
     */
    public int updateKnowledgeBasePermission(KnowledgeBasePermission knowledgeBasePermission);

    /**
     * 删除知识库权限
     * 
     * @param id 知识库权限主键
     * @return 结果
     */
    public int deleteKnowledgeBasePermissionById(Long id);

    /**
     * 批量删除知识库权限
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKnowledgeBasePermissionByIds(Long[] ids);

    /**
     * 根据知识库ID删除权限
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 结果
     */
    public int deleteKnowledgeBasePermissionByKnowledgeBaseId(Long knowledgeBaseId);

    /**
     * 根据用户ID删除权限
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteKnowledgeBasePermissionByUserId(Long userId);

    /**
     * 批量插入知识库权限
     * 
     * @param permissions 知识库权限列表
     * @return 结果
     */
    public int batchInsertKnowledgeBasePermission(List<KnowledgeBasePermission> permissions);
}
