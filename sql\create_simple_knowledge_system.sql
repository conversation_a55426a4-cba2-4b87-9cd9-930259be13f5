-- =====================================================
-- 简化版我的知识库系统创建脚本
-- 用途：基于若依原生表结构，创建简化的知识库权限管理
-- 设计理念：复用现有用户角色体系，减少表结构冗余
-- 创建时间：2025-08-22
-- =====================================================

-- 设置安全模式，防止误操作
SET SQL_SAFE_UPDATES = 0;

-- =====================================================
-- 第一部分：简化的知识库权限表
-- 说明：只存储核心权限信息，用户信息通过关联查询获取
-- =====================================================

-- 删除之前创建的复杂权限表（如果存在）
DROP TABLE IF EXISTS `knowledge_base_permission`;

-- 创建简化的知识库权限表
CREATE TABLE `knowledge_base_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `knowledge_base_id` bigint NOT NULL COMMENT '知识库ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `permission_type` varchar(10) NOT NULL DEFAULT 'read' COMMENT '权限类型（read只读，write读写，admin管理员）',
  `create_by` varchar(64) DEFAULT 'system' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_kb_user` (`knowledge_base_id`, `user_id`),
  KEY `idx_knowledge_base_id` (`knowledge_base_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_kb_permission_kb_simple` FOREIGN KEY (`knowledge_base_id`) REFERENCES `knowledge_base` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_kb_permission_user_simple` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='知识库权限表（简化版）';

-- =====================================================
-- 第二部分：添加知识库管理员角色（复用sys_role表）
-- =====================================================

-- 添加知识库管理员角色
INSERT INTO `sys_role` (
    `role_name`, 
    `role_key`, 
    `role_sort`, 
    `data_scope`, 
    `menu_check_strictly`, 
    `dept_check_strictly`, 
    `status`, 
    `del_flag`, 
    `create_by`, 
    `create_time`, 
    `remark`
) VALUES (
    '知识库管理员', 
    'knowledge_admin', 
    4, 
    '2', 
    1, 
    1, 
    '0', 
    '0', 
    'admin', 
    NOW(), 
    '知识库管理员：可管理知识库权限，授予其他用户访问权限'
) ON DUPLICATE KEY UPDATE role_name = VALUES(role_name);

-- 获取知识库管理员角色ID
SET @knowledge_admin_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'knowledge_admin');

-- =====================================================
-- 第三部分：创建菜单结构（复用sys_menu表）
-- =====================================================

-- 1. 创建"我的知识库"顶级菜单
INSERT INTO `sys_menu` (
    `menu_name`, 
    `parent_id`, 
    `order_num`, 
    `path`, 
    `component`, 
    `is_frame`, 
    `is_cache`, 
    `menu_type`, 
    `visible`, 
    `status`, 
    `perms`, 
    `icon`, 
    `create_by`, 
    `create_time`, 
    `remark`
) VALUES (
    '我的知识库', 
    0, 
    6, 
    'my-knowledge', 
    NULL, 
    1, 
    0, 
    'M', 
    '0', 
    '0', 
    NULL, 
    'folder', 
    'admin', 
    NOW(), 
    '我的知识库管理目录'
) ON DUPLICATE KEY UPDATE menu_name = VALUES(menu_name);

-- 获取"我的知识库"菜单ID
SET @my_knowledge_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '我的知识库' AND parent_id = 0);

-- 2. 创建"知识库列表"子菜单
INSERT INTO `sys_menu` (
    `menu_name`, 
    `parent_id`, 
    `order_num`, 
    `path`, 
    `component`, 
    `is_frame`, 
    `is_cache`, 
    `menu_type`, 
    `visible`, 
    `status`, 
    `perms`, 
    `icon`, 
    `create_by`, 
    `create_time`, 
    `remark`
) VALUES (
    '知识库列表', 
    @my_knowledge_menu_id, 
    1, 
    'list', 
    'knowledge/my/index', 
    1, 
    0, 
    'C', 
    '0', 
    '0', 
    'knowledge:my:list', 
    'list', 
    'admin', 
    NOW(), 
    '我的知识库列表页面'
) ON DUPLICATE KEY UPDATE menu_name = VALUES(menu_name);

-- 3. 创建"权限管理"子菜单
INSERT INTO `sys_menu` (
    `menu_name`, 
    `parent_id`, 
    `order_num`, 
    `path`, 
    `component`, 
    `is_frame`, 
    `is_cache`, 
    `menu_type`, 
    `visible`, 
    `status`, 
    `perms`, 
    `icon`, 
    `create_by`, 
    `create_time`, 
    `remark`
) VALUES (
    '知识库权限', 
    @my_knowledge_menu_id, 
    2, 
    'permission', 
    'knowledge/permission/index', 
    1, 
    0, 
    'C', 
    '0', 
    '0', 
    'knowledge:permission:list', 
    'lock', 
    'admin', 
    NOW(), 
    '知识库权限管理页面'
) ON DUPLICATE KEY UPDATE menu_name = VALUES(menu_name);

-- =====================================================
-- 第四部分：创建按钮权限（复用sys_menu表）
-- =====================================================

-- 获取菜单ID
SET @my_kb_list_menu_id = (SELECT menu_id FROM sys_menu WHERE perms = 'knowledge:my:list');
SET @kb_permission_menu_id = (SELECT menu_id FROM sys_menu WHERE perms = 'knowledge:permission:list');

-- 为"知识库列表"添加按钮权限
INSERT INTO `sys_menu` (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time) VALUES 
('查看知识库', @my_kb_list_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'knowledge:my:query', '#', 'admin', NOW()),
('创建知识库', @my_kb_list_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'knowledge:my:add', '#', 'admin', NOW()),
('编辑知识库', @my_kb_list_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'knowledge:my:edit', '#', 'admin', NOW()),
('删除知识库', @my_kb_list_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'knowledge:my:remove', '#', 'admin', NOW())
ON DUPLICATE KEY UPDATE menu_name = VALUES(menu_name);

-- 为"权限管理"添加按钮权限
INSERT INTO `sys_menu` (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time) VALUES 
('查看权限', @kb_permission_menu_id, 1, '', '', 1, 0, 'F', '0', '0', 'knowledge:permission:query', '#', 'admin', NOW()),
('授予权限', @kb_permission_menu_id, 2, '', '', 1, 0, 'F', '0', '0', 'knowledge:permission:add', '#', 'admin', NOW()),
('修改权限', @kb_permission_menu_id, 3, '', '', 1, 0, 'F', '0', '0', 'knowledge:permission:edit', '#', 'admin', NOW()),
('撤销权限', @kb_permission_menu_id, 4, '', '', 1, 0, 'F', '0', '0', 'knowledge:permission:remove', '#', 'admin', NOW())
ON DUPLICATE KEY UPDATE menu_name = VALUES(menu_name);

-- =====================================================
-- 第五部分：分配角色权限（复用sys_role_menu表）
-- =====================================================

-- 为超级管理员分配所有新菜单权限
INSERT IGNORE INTO `sys_role_menu` (`role_id`, `menu_id`)
SELECT 1, `menu_id` FROM `sys_menu` 
WHERE `perms` LIKE 'knowledge:my:%' OR `perms` LIKE 'knowledge:permission:%'
   OR `menu_id` = @my_knowledge_menu_id;

-- 为知识库管理员分配相应权限
INSERT IGNORE INTO `sys_role_menu` (`role_id`, `menu_id`)
SELECT @knowledge_admin_role_id, `menu_id` FROM `sys_menu` 
WHERE `perms` LIKE 'knowledge:my:%' OR `perms` LIKE 'knowledge:permission:%'
   OR `menu_id` = @my_knowledge_menu_id;

-- =====================================================
-- 第六部分：初始化权限数据
-- =====================================================

-- 为现有知识库的创建者自动添加管理员权限
INSERT IGNORE INTO `knowledge_base_permission` (
    `knowledge_base_id`, 
    `user_id`, 
    `permission_type`, 
    `create_by`, 
    `create_time`
)
SELECT 
    kb.id,
    kb.creator_id,
    'admin',
    'system',
    NOW()
FROM knowledge_base kb
WHERE kb.creator_id IS NOT NULL;

-- =====================================================
-- 验证创建结果
-- =====================================================

-- 查看简化的权限表结构
DESCRIBE knowledge_base_permission;

-- 查看创建的菜单结构
SELECT 
    menu_id,
    menu_name,
    parent_id,
    CASE 
        WHEN parent_id = 0 THEN '顶级菜单'
        ELSE CONCAT('子菜单(', (SELECT menu_name FROM sys_menu p WHERE p.menu_id = m.parent_id), ')')
    END as menu_level,
    path,
    component,
    perms,
    order_num
FROM sys_menu m
WHERE menu_name LIKE '%我的知识库%' 
   OR menu_name LIKE '%知识库权限%'
   OR perms LIKE 'knowledge:my:%'
   OR perms LIKE 'knowledge:permission:%'
   OR parent_id = @my_knowledge_menu_id
ORDER BY parent_id, order_num;

-- 查看初始化的权限数据（关联查询用户信息）
SELECT 
    kbp.id,
    kb.name as knowledge_base_name,
    u.user_name,
    u.nick_name,
    kbp.permission_type,
    kbp.create_time
FROM knowledge_base_permission kbp
JOIN knowledge_base kb ON kbp.knowledge_base_id = kb.id
JOIN sys_user u ON kbp.user_id = u.user_id
ORDER BY kbp.create_time DESC;

-- 恢复安全模式
SET SQL_SAFE_UPDATES = 1;

-- =====================================================
-- 简化版系统说明
-- =====================================================
/*
✅ 简化版我的知识库系统创建完成！

设计优势：
1. 复用若依原生表结构，减少数据冗余
2. 权限表只存储核心信息，用户信息通过关联查询
3. 利用现有的用户角色体系，无需额外的用户管理
4. 表结构简洁，维护成本低

表结构对比：
- 原方案：knowledge_base_permission (15个字段)
- 简化版：knowledge_base_permission (8个字段)
- 减少字段：user_name, knowledge_base_name, granted_by等冗余字段

功能保持：
- 权限管理功能完全保留
- 菜单结构完整
- 角色权限分配正常
- 用户体验不变

后续开发：
- 后端API通过JOIN查询获取用户和知识库信息
- 前端页面显示效果相同
- 数据一致性更好，维护更简单
*/
