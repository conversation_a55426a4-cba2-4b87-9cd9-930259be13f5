-- =====================================================
-- 数据库表优化脚本
-- 用途：删除未使用的数据库表，优化数据库结构
-- 创建时间：2025-08-22
-- 注意：执行前请备份数据库！
-- =====================================================

-- 设置安全模式，防止误操作
SET SQL_SAFE_UPDATES = 0;

-- 临时禁用外键检查，避免删除顺序问题
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 第一部分：删除 Quartz 定时任务相关表（11个表）
-- 说明：项目使用内存调度器，不需要这些持久化表
-- 注意：按照外键依赖关系的正确顺序删除
-- =====================================================

-- 1. 删除依赖表（有外键约束的表先删除）
DROP TABLE IF EXISTS `qrtz_blob_triggers`;
DROP TABLE IF EXISTS `qrtz_cron_triggers`;
DROP TABLE IF EXISTS `qrtz_simple_triggers`;
DROP TABLE IF EXISTS `qrtz_simprop_triggers`;

-- 2. 删除主触发器表
DROP TABLE IF EXISTS `qrtz_triggers`;

-- 3. 删除任务详情表
DROP TABLE IF EXISTS `qrtz_job_details`;

-- 4. 删除其他独立表
DROP TABLE IF EXISTS `qrtz_calendars`;
DROP TABLE IF EXISTS `qrtz_fired_triggers`;
DROP TABLE IF EXISTS `qrtz_locks`;
DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;
DROP TABLE IF EXISTS `qrtz_scheduler_state`;

-- =====================================================
-- 第二部分：删除代码生成器相关表（生产环境推荐删除）
-- 说明：这些表主要用于开发阶段，生产环境通常不需要
-- =====================================================

-- 删除代码生成业务表字段信息表（先删除子表）
DROP TABLE IF EXISTS `gen_table_column`;

-- 删除代码生成业务表信息表（后删除主表）
DROP TABLE IF EXISTS `gen_table`;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 验证删除结果
-- =====================================================

-- 查看剩余的表
SELECT 
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表注释',
    TABLE_ROWS as '行数',
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) as '大小(MB)'
FROM 
    information_schema.TABLES 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND TABLE_TYPE = 'BASE TABLE'
ORDER BY 
    TABLE_NAME;

-- 统计删除的表数量
SELECT
    '已删除 Quartz 相关表' as '操作',
    11 as '表数量',
    '内存调度器不需要持久化表' as '说明'
UNION ALL
SELECT
    '已删除代码生成器表' as '操作',
    2 as '表数量',
    '生产环境不需要代码生成功能' as '说明'
UNION ALL
SELECT
    '总计删除表数量' as '操作',
    13 as '表数量',
    '优化完成，数据库更加精简' as '说明';

-- 恢复安全模式
SET SQL_SAFE_UPDATES = 1;

-- =====================================================
-- 优化建议
-- =====================================================
/*
优化效果：
1. 空间节省：删除 13 个未使用表，节省数据库空间和维护成本
2. 性能提升：减少表数量可以提高数据库查询性能
3. 维护简化：减少不必要的表结构维护工作
4. 安全性：减少潜在的安全风险点

删除的表：
- 11个 Quartz 持久化表：qrtz_*
- 2个 代码生成器表：gen_table, gen_table_column

注意事项：
- 如果将来需要集群部署定时任务，可能需要重新创建 Quartz 表
- 如果生产环境需要代码生成功能，可以从回滚脚本恢复 gen_* 表
- 执行前务必备份数据库
*/
